/**
 * 速率限制实现
 *
 * 使用令牌桶和自适应算法的高级速率限制系统，用于防止滥用并确保公平的资源分配。
 * 支持基于系统负载的固定速率和自适应速率限制。
 *
 * @fileoverview 令牌桶和自适应速率限制实现
 * <AUTHOR> MCP 团队
 * @since 1.0.0
 */

/**
 * 令牌桶速率限制器
 *
 * 实现用于速率限制的令牌桶算法。该算法允许突发流量，
 * 同时在一段时间内维持平均速率限制。
 *
 * 算法详情：
 * - 令牌以恒定速率（补充速率）添加到桶中
 * - 每个请求消耗一个或多个令牌
 * - 只有在有足够令牌可用时才允许请求
 * - 桶有最大容量以限制突发大小
 *
 * 优势：
 * - 允许高达桶容量的突发流量
 * - 平滑的速率限制，无严格的时间窗口
 * - 内存高效，具有 O(1) 空间复杂度
 * - 基于实际使用模式的自我调节
 *
 * @class TokenBucketRateLimiter
 * @since 1.0.0
 */
export class TokenBucketRateLimiter {
  /** 桶可以容纳的最大令牌数 */
  private capacity: number;

  /** 桶中当前的令牌数 */
  private tokens: number;

  /** 添加令牌的速率（每秒令牌数） */
  private refillRate: number;

  /** 速率计算的时间窗口（秒） */
  private window: number;

  /** 最后一次令牌补充操作的时间戳 */
  private lastRefill: number;

  /**
   * Token Bucket Constructor
   *
   * Initializes the token bucket with specified capacity and refill rate.
   * The bucket starts full to allow immediate requests.
   *
   * @constructor
   * @param {number} capacity - Maximum tokens the bucket can hold
   * @param {number} refillRate - Tokens added per second
   * @param {number} [window=60] - Time window in seconds (for compatibility)
   *
   * @example
   * // Allow 100 requests with refill of 10 per second
   * const limiter = new TokenBucketRateLimiter(100, 10);
   */
  constructor(capacity: number, refillRate: number, window: number = 60) {
    this.capacity = capacity;
    this.tokens = capacity; // Start with full bucket
    this.refillRate = refillRate;
    this.window = window;
    this.lastRefill = Date.now();
  }

  /**
   * Check if Request is Allowed
   *
   * Determines whether a request can be processed based on token availability.
   * Automatically refills tokens based on elapsed time and consumes tokens
   * for allowed requests.
   *
   * Algorithm Steps:
   * 1. Calculate elapsed time since last refill
   * 2. Add tokens based on refill rate and elapsed time
   * 3. Check if sufficient tokens are available
   * 4. Consume tokens if request is allowed
   *
   * Time Complexity: O(1)
   * Space Complexity: O(1)
   *
   * @public
   * @param {number} [tokensRequested=1] - Number of tokens to consume
   * @returns {boolean} True if request is allowed, false if rate limited
   *
   * @example
   * if (limiter.allowRequest()) {
   *   // Process request
   *   processRequest();
   * } else {
   *   // Rate limited
   *   throw new Error('Rate limit exceeded');
   * }
   */
  public allowRequest(tokensRequested: number = 1): boolean {
    const now = Date.now();

    // 根据经过的时间计算要添加的令牌数
    const elapsed = (now - this.lastRefill) / 1000; // 转换为秒
    const tokensToAdd = elapsed * this.refillRate;

    // 将桶补充到容量上限
    this.tokens = Math.min(this.capacity, this.tokens + tokensToAdd);
    this.lastRefill = now;

    // 检查是否有足够的令牌可用
    if (this.tokens >= tokensRequested) {
      this.tokens -= tokensRequested;
      return true;
    }

    return false;
  }
}

/**
 * 自适应速率限制器
 *
 * 高级速率限制系统，根据系统负载和资源利用率自动调整限制。
 * 为不同标识符维护独立的令牌桶，同时适应系统条件。
 *
 * 功能特性：
 * - 基于 CPU 和内存使用情况的动态速率调整
 * - 具有隔离桶的按标识符速率限制
 * - 自动桶创建和管理
 * - 负载感知缩放以获得最佳性能
 *
 * 使用场景：
 * - 具有系统负载感知的 API 速率限制
 * - 数据库连接节流
 * - 资源感知的请求处理
 * - 多租户速率限制
 *
 * @class AdaptiveRateLimiter
 * @since 1.0.0
 */
export class AdaptiveRateLimiter {
  /** Base rate limit before system load adjustments */
  private baseLimit: number;

  /** Time window for rate calculations (seconds) */
  private window: number;

  /** Current system load factor (0.5 to 1.2) */
  private systemLoadFactor: number;

  /** Map of identifier-specific token bucket limiters */
  private buckets: Map<string, TokenBucketRateLimiter>;

  /**
   * Adaptive Rate Limiter Constructor
   *
   * Initializes the adaptive rate limiter with base limits and time window.
   * System load factor starts at 1.0 (no adjustment).
   *
   * @constructor
   * @param {number} baseLimit - Base rate limit before load adjustments
   * @param {number} [window=60] - Time window in seconds
   *
   * @example
   * // Create adaptive limiter with 100 requests per minute
   * const limiter = new AdaptiveRateLimiter(100, 60);
   */
  constructor(baseLimit: number, window: number = 60) {
    this.baseLimit = baseLimit;
    this.window = window;
    this.systemLoadFactor = 1.0; // Start with no adjustment
    this.buckets = new Map<string, TokenBucketRateLimiter>();
  }

  /**
   * Update System Load
   *
   * Adjusts the system load factor based on CPU and memory utilization.
   * This affects all rate limits by scaling the base limit up or down.
   *
   * Load Factor Rules:
   * - High load (CPU > 80% OR Memory > 80%): Factor = 0.5 (reduce limits)
   * - Low load (CPU < 50% AND Memory < 50%): Factor = 1.2 (increase limits)
   * - Normal load: Factor = 1.0 (no adjustment)
   *
   * @public
   * @param {number} cpuUsage - CPU utilization (0.0 to 1.0)
   * @param {number} memoryUsage - Memory utilization (0.0 to 1.0)
   *
   * @example
   * // Update based on system metrics
   * limiter.updateSystemLoad(0.75, 0.60); // Normal load
   * limiter.updateSystemLoad(0.90, 0.85); // High load - reduces limits
   */
  public updateSystemLoad(cpuUsage: number, memoryUsage: number): void {
    if (cpuUsage > 0.8 || memoryUsage > 0.8) {
      // High system load: reduce rate limits to protect system
      this.systemLoadFactor = 0.5;
    } else if (cpuUsage < 0.5 && memoryUsage < 0.5) {
      // Low system load: increase rate limits for better throughput
      this.systemLoadFactor = 1.2;
    } else {
      // Normal system load: use base rate limits
      this.systemLoadFactor = 1.0;
    }
  }

  /**
   * Check Rate Limit for Identifier
   *
   * Checks if a request from the specified identifier should be allowed
   * based on current rate limits and system load. Creates new token buckets
   * for new identifiers automatically.
   *
   * @public
   * @param {string} identifier - Unique identifier for rate limiting
   * @returns {boolean} True if request is allowed, false if rate limited
   *
   * @example
   * // Check rate limit for specific user
   * if (limiter.checkRateLimit('user:123')) {
   *   // Process request
   *   handleRequest();
   * } else {
   *   // Rate limited
   *   throw new Error('Rate limit exceeded for user');
   * }
   */
  public checkRateLimit(identifier: string): boolean {
    // Calculate adjusted limit based on current system load
    const adjustedLimit = Math.floor(this.baseLimit * this.systemLoadFactor);

    // Create new token bucket for new identifiers
    if (!this.buckets.has(identifier)) {
      const refillRate = adjustedLimit / this.window;
      this.buckets.set(identifier, new TokenBucketRateLimiter(
        adjustedLimit,
        refillRate,
        this.window
      ));
    }

    // Check rate limit using the identifier's token bucket
    return this.buckets.get(identifier)!.allowRequest();
  }
}